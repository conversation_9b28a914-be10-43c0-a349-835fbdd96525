import { Component, inject, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormControl } from '@angular/forms';
import { delay, lastValueFrom, Observable, of } from 'rxjs';
import { AtlasConfigService } from '../../../core/src';
import { switchMap } from 'rxjs/operators';

interface Entitlement {
  id: string;
  name: string;
  description: string;
}

@Component({
  selector: 'atlas-pam-dialog',
  templateUrl: './pam-dialog.component.html',
  styleUrl: './pam-dialog.component.scss',
  standalone: false,
})
export class PamDialogComponent implements OnInit {
  private dialogRef = inject(MatDialogRef<PamDialogComponent>);
  private configService = inject(AtlasConfigService);

  public reason = new FormControl('');
  public entitlement$: Observable<Entitlement> = null;

  ngOnInit(): void {
    this.entitlement$ = this.configService.config$.pipe(
      switchMap((config) => this.getEntitlement$(config.entitlementId)),
    );
  }

  getEntitlement$(entitlementId: string): Observable<Entitlement> {
    // mock entitlement request
    // will be replaced with actual an request when SDKs become available
    return of({
      id: entitlementId,
      name: 'Entitlement Name',
      description: 'This is a mock entitlement description',
    }).pipe(delay(500));
  }

  async createGrant() {
    try {
      // use a mock request for now until APIs and SDKs are available
      await lastValueFrom(this.fakeCreateGrantRequest$({ reason: this.reason.value }));
      // close dialog if grant is created successfully
      // since close is disabled, this is the only way the dialog can be closed
      this.dialogRef.close();
    } catch (error) {
      // open error snack if error occurs
    }
  }

  fakeCreateGrantRequest$(fakeRequest: { reason: string }): Observable<{ message: string; reason: string }> {
    return of({ message: 'Grant creation successful', reason: fakeRequest.reason }).pipe(delay(500));
  }
}
