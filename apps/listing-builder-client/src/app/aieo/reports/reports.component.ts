import { Component, OnInit, ChangeDetectionStrategy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Mat<PERSON>ard, MatCardContent, Mat<PERSON>ardHeader, MatCardTitle } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { SharedModule } from '../../shared/shared.module';
import { IconComponent } from '@vendasta/uikit';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { ReportsService } from '../reports/reports.service';
import { AuditReport } from '../reports/reports.interface';
import { Observable, take, takeUntil, Subject } from 'rxjs';
import { AGIDTOKEN } from '../../app.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    SharedModule,
    IconComponent,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatListModule,
    MatDividerModule,
    MatProgressBarModule,
    GalaxyBadgeModule,
    TranslateModule,
  ],
  standalone: true,
})
export class ReportsComponent implements OnInit {
  reports: AuditReport[] = [];
  loading = false;
  private destroy$ = new Subject<void>();

  constructor(
    private reportsService: ReportsService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(AGIDTOKEN) private accountGroupId$: Observable<string>,
  ) {}

  ngOnInit(): void {
    this.loadReports();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadReports(): void {
    this.loading = true;

    this.accountGroupId$.pipe(take(1), takeUntil(this.destroy$)).subscribe({
      next: (accountGroupId) => {
        this.reportsService.loadReportsFromAPI(accountGroupId).subscribe({
          next: (reports) => {
            this.reports = reports;
            this.loading = false;
          },
          error: (_error) => {
            this.loading = false;
            this.reportsService
              .getReports()
              .pipe(take(1))
              .subscribe((reports) => {
                this.reports = reports;
              });
          },
        });
      },
      error: (_error) => {
        this.loading = false;
      },
    });
  }

  onViewResults(report: AuditReport): void {
    this.reportsService.setCurrentReport(report);
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo/results`]);
    });
  }

  onRunNewAudit(): void {
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return 'AIEO.REPORTS.STATUS.COMPLETED';
      case 'in_progress':
        return 'AIEO.REPORTS.STATUS.IN_PROGRESS';
      case 'failed':
        return 'AIEO.REPORTS.STATUS.FAILED';
      default:
        return 'AIEO.REPORTS.STATUS.PENDING';
    }
  }

  trackByReportId(index: number, report: AuditReport): string {
    return report.id;
  }
}
