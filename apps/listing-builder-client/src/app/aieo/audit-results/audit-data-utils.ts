import { MultiPageAuditData, PageAuditData, AuditSection } from './audit-consolidation.service';

/**
 * Utility functions for transforming audit data into the format expected by the consolidation service
 */
export class AuditDataUtils {
  
  /**
   * Transforms an array of individual page audit data into the MultiPageAuditData format
   * @param pages Array of page audit data objects
   * @param overallScore Overall website score
   * @param overallDescription Overall website description
   */
  static transformToMultiPageData(
    pages: Array<{
      url: string;
      sections: AuditSection[];
    }>,
    overallScore: number,
    overallDescription: string
  ): MultiPageAuditData {
    return {
      overallScore,
      overallDescription,
      pages: pages.map(page => ({
        url: page.url,
        sections: page.sections
      }))
    };
  }

  /**
   * Transforms a single audit data object into MultiPageAuditData format
   * Useful when you have data for just one page
   */
  static transformSinglePageData(
    auditData: {
      overallScore: number;
      description: string;
      sections: AuditSection[];
    },
    url: string
  ): MultiPageAuditData {
    return {
      overallScore: auditData.overallScore,
      overallDescription: auditData.description,
      pages: [{
        url,
        sections: auditData.sections
      }]
    };
  }

  /**
   * Transforms raw JSON audit data into the expected format
   * This is useful when you have audit data in a different JSON structure
   */
  static transformFromRawJson(rawData: any): MultiPageAuditData {
    // Handle different possible JSON structures
    if (Array.isArray(rawData)) {
      // If it's an array, assume each item is a page
      const pages: PageAuditData[] = rawData.map((pageData, index) => ({
        url: pageData.url || `Page ${index + 1}`,
        sections: this.normalizeSections(pageData.sections || pageData.categories || [])
      }));

      // Calculate overall score as average of all page scores
      const totalScore = pages.reduce((sum, page) => {
        const pageScore = page.sections.reduce((sectionSum, section) => sectionSum + section.score, 0) / page.sections.length;
        return sum + pageScore;
      }, 0);
      const overallScore = Math.round(totalScore / pages.length);

      return {
        overallScore,
        overallDescription: 'Consolidated audit results across multiple pages',
        pages
      };
    } else if (rawData.pages) {
      // If it already has a pages structure
      return {
        overallScore: rawData.overallScore || 0,
        overallDescription: rawData.overallDescription || rawData.description || '',
        pages: rawData.pages.map((page: any) => ({
          url: page.url,
          sections: this.normalizeSections(page.sections || page.categories || [])
        }))
      };
    } else {
      // Single page data
      return this.transformSinglePageData(
        {
          overallScore: rawData.overallScore || 0,
          description: rawData.description || '',
          sections: this.normalizeSections(rawData.sections || rawData.categories || [])
        },
        rawData.url || 'Unknown Page'
      );
    }
  }

  /**
   * Normalizes sections to ensure they have the expected structure
   */
  private static normalizeSections(sections: any[]): AuditSection[] {
    return sections.map(section => ({
      title: section.title || section.name || 'Untitled Section',
      score: typeof section.score === 'number' ? section.score : 0,
      recommendations: Array.isArray(section.recommendations) ? section.recommendations : [],
      summary: section.summary || section.description || ''
    }));
  }

  /**
   * Validates that the audit data has the correct structure
   */
  static validateAuditData(data: MultiPageAuditData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data) {
      errors.push('Audit data is null or undefined');
      return { isValid: false, errors };
    }

    if (typeof data.overallScore !== 'number' || data.overallScore < 0 || data.overallScore > 100) {
      errors.push('Overall score must be a number between 0 and 100');
    }

    if (!data.overallDescription || typeof data.overallDescription !== 'string') {
      errors.push('Overall description is required and must be a string');
    }

    if (!Array.isArray(data.pages) || data.pages.length === 0) {
      errors.push('Pages array is required and must not be empty');
    } else {
      data.pages.forEach((page, index) => {
        if (!page.url || typeof page.url !== 'string') {
          errors.push(`Page ${index + 1}: URL is required and must be a string`);
        }

        if (!Array.isArray(page.sections)) {
          errors.push(`Page ${index + 1}: Sections must be an array`);
        } else {
          page.sections.forEach((section, sectionIndex) => {
            if (!section.title || typeof section.title !== 'string') {
              errors.push(`Page ${index + 1}, Section ${sectionIndex + 1}: Title is required and must be a string`);
            }

            if (typeof section.score !== 'number' || section.score < 0 || section.score > 100) {
              errors.push(`Page ${index + 1}, Section ${sectionIndex + 1}: Score must be a number between 0 and 100`);
            }

            if (!Array.isArray(section.recommendations)) {
              errors.push(`Page ${index + 1}, Section ${sectionIndex + 1}: Recommendations must be an array`);
            }

            if (typeof section.summary !== 'string') {
              errors.push(`Page ${index + 1}, Section ${sectionIndex + 1}: Summary must be a string`);
            }
          });
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }


} 