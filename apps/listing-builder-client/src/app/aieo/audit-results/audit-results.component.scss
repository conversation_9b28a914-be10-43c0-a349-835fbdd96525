@use 'design-tokens' as *;

.audit-results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-4;
  background: $primary-background-color;
  min-height: 100vh;
}

.audit-section {
  margin-bottom: $spacing-5;
}

.audit-card {
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  border: 1px solid $border-color;
  overflow: hidden;
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 $spacing-1 $spacing-1 -1px rgba($primary-color, 0.1);
  }
}

.overall-score-card {
  background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($primary-color, 0.02) 100%);
  border: 2px solid rgba($primary-color, 0.2);
}

.audit-card-content {
  padding: $spacing-5;
}

.progress-card-layout {
  display: flex;
  align-items: center;
  gap: $spacing-5;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-4;
  }
}

.progress-column {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circular-gauge-container {
  position: relative;
  width: 160px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.6s ease-out;

  @media (max-width: $mobile-breakpoint-max) {
    width: 140px;
    height: 140px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 120px;
    height: 120px;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.gauge-background {
  fill: none;
  stroke: var(--border-color, #e0e0e0);
  stroke-width: 6;
  stroke-linecap: round;
  opacity: 0.3;
}

.gauge-progress {
  fill: none;
  stroke-width: 6;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
}

.gauge-score {
  @include text-preset-1;
  font-weight: 700;
  color: $primary-text-color;
  margin: 0;
  line-height: 1;
  font-size: 2rem;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 1.75rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 1.5rem;
  }
}

.gauge-label {
  @include text-preset-5;
  color: $secondary-text-color;
  margin: $spacing-1 0 0 0;
  font-weight: 500;
  letter-spacing: 0.5px;
  font-size: 0.875rem;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 0.8rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 0.75rem;
  }
}

.content-column {
  flex: 1;
  min-width: 0;
}

.audit-summary {
  .overall-score-title {
    @include text-preset-2;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-2 0;
  }

  .audit-description {
    @include text-preset-4;
    color: $secondary-text-color;
    margin: 0;
    line-height: 1.6;
  }
}

.audit-section-header {
  margin-bottom: $spacing-3;
}

.section-title {
  @include text-preset-3;
  font-weight: 600;
  color: $primary-text-color;
  margin: 0;
}

.audit-section-content {
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  padding: $spacing-4;
  border: 1px solid $border-color;
}

.category-layout {
  display: flex;
  align-items: flex-start;
  gap: $spacing-4;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.category-gauge-column {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.category-gauge-container {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.6s ease-out;

  @media (max-width: $mobile-breakpoint-max) {
    width: 100px;
    height: 100px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 80px;
    height: 80px;
  }
}

.category-gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.category-gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.category-gauge-background {
  fill: none;
  stroke: var(--border-color, #e0e0e0);
  stroke-width: 4;
  stroke-linecap: round;
  opacity: 0.3;
}

.category-gauge-progress {
  fill: none;
  stroke-width: 4;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.category-gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
}

.category-gauge-score {
  @include text-preset-3;
  font-weight: 700;
  color: $primary-text-color;
  margin: 0;
  line-height: 1;
  font-size: 1.25rem;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 1.125rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 1rem;
  }
}

.category-gauge-label {
  @include text-preset-5;
  color: $secondary-text-color;
  margin: $spacing-1 0 0 0;
  font-weight: 500;
  letter-spacing: 0.25px;
  font-size: 0.75rem;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 0.7rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 0.65rem;
  }
}

.category-content-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.recommendations-section,
.summary-section {
  .recommendations-title,
  .summary-title {
    @include text-preset-4;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-2 0;
  }
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.recommendation-item {
  @include text-preset-4;
  color: $primary-text-color;
  line-height: 1.5;
  padding: $spacing-1 0;
  border-bottom: 1px solid rgba($border-color, 0.3);

  &:last-child {
    border-bottom: none;
  }
}

.summary-content {
  @include text-preset-4;
  color: $secondary-text-color;
  line-height: 1.6;
  margin: 0;
}

.audit-footer {
  display: flex;
  justify-content: center;
  margin-top: $spacing-5;
  padding-top: $spacing-4;
  border-top: 1px solid $border-color;
}

.run-another-button {
  min-width: 160px;
  height: 48px;
  @include text-preset-4;
  font-weight: 600;
  border-radius: $default-border-radius * 2;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    transform: translateY($negative-1);
    box-shadow: 0 $spacing-1 $spacing-2 -1px rgba(0, 0, 0, 0.1);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 $spacing-2 $spacing-2 -1px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: $mobile-breakpoint-max) {
    min-width: 140px;
    height: 44px;
  }

  @media (max-width: $media--phone-large-minimum) {
    min-width: 120px;
    height: 40px;
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .audit-results-container {
    padding: $spacing-3;
  }

  .audit-card-content {
    padding: $spacing-4;
  }

  .progress-card-layout {
    gap: $spacing-3;
  }

  .circular-gauge-container {
    width: 140px;
    height: 140px;
  }

  .gauge-score {
    font-size: 1.75rem;
  }

  .gauge-label {
    font-size: 0.8rem;
  }

  .audit-section-content {
    padding: $spacing-3;
  }

  .category-layout {
    gap: $spacing-3;
  }

  .category-gauge-container {
    width: 100px;
    height: 100px;
  }

  .category-gauge-score {
    font-size: 1.125rem;
  }

  .category-gauge-label {
    font-size: 0.7rem;
  }

  .run-another-button {
    min-width: 140px;
    height: 44px;
  }
}

@media (max-width: $media--phone-large-minimum) {
  .audit-results-container {
    padding: $spacing-2;
  }

  .audit-card-content {
    padding: $spacing-3;
  }

  .progress-card-layout {
    gap: $spacing-2;
  }

  .circular-gauge-container {
    width: 120px;
    height: 120px;
  }

  .gauge-score {
    font-size: 1.5rem;
  }

  .gauge-label {
    font-size: 0.75rem;
  }

  .audit-section-content {
    padding: $spacing-2;
  }

  .category-layout {
    gap: $spacing-2;
  }

  .category-gauge-container {
    width: 80px;
    height: 80px;
  }

  .category-gauge-score {
    font-size: 1rem;
  }

  .category-gauge-label {
    font-size: 0.65rem;
  }

  .run-another-button {
    min-width: 120px;
    height: 40px;
  }
}
