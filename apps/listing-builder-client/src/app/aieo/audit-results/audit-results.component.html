<div class="audit-results-container" *ngIf="auditData">
  <!-- Overall Score Section -->
  <div class="audit-section">
    <mat-card appearance="outlined" class="audit-card overall-score-card">
      <mat-card-content class="audit-card-content">
        <div class="progress-card-layout">
          <div class="progress-column">
            <div class="circular-gauge-container">
              <div
                class="gauge-wrapper"
                role="progressbar"
                [attr.aria-valuenow]="overallScore"
                aria-valuemin="0"
                aria-valuemax="100"
                [attr.aria-label]="'AIEO.RESULTS.ARIA_LABEL' | translate: { score: overallScore }"
              >
                <svg class="gauge-svg" viewBox="0 0 140 140" xmlns="http://www.w3.org/2000/svg">
                  <circle
                    class="gauge-background"
                    cx="70"
                    cy="70"
                    r="55"
                    fill="none"
                    stroke="var(--border-color, #e0e0e0)"
                    stroke-width="6"
                  />
                  <circle
                    class="gauge-progress"
                    cx="70"
                    cy="70"
                    r="55"
                    fill="none"
                    stroke="url(#gauge-gradient)"
                    stroke-width="6"
                    stroke-linecap="round"
                    [attr.stroke-dasharray]="getGaugeCircumference()"
                    [attr.stroke-dashoffset]="getGaugeOffset()"
                    transform="rotate(-90 70 70)"
                  />
                  <defs>
                    <linearGradient id="gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" [attr.stop-color]="getGaugeColor(0)" />
                      <stop offset="50%" [attr.stop-color]="getGaugeColor(50)" />
                      <stop offset="100%" [attr.stop-color]="getGaugeColor(100)" />
                    </linearGradient>
                  </defs>
                </svg>
                <div class="gauge-center">
                  <div class="gauge-score">{{ overallScore }}%</div>
                  <div class="gauge-label">{{ scoreLabel }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-column">
            <div class="audit-summary">
              <h2 class="overall-score-title">Overall Score</h2>
              <p class="audit-description">
                {{ auditData?.description || '' }}
              </p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Category Sections -->
  <div class="audit-section" *ngFor="let section of sections; trackBy: trackBySection">
    <div class="audit-section-header">
      <h3 class="section-title">{{ section?.title || ('AIEO.RESULTS.UNTITLED_SECTION' | translate) }}</h3>
    </div>
    <div class="audit-section-content">
      <div class="category-layout">
        <!-- Left Side - Score Display -->
        <div class="category-gauge-column">
          <div class="category-gauge-container">
            <div
              class="category-gauge-wrapper"
              role="progressbar"
              [attr.aria-valuenow]="section?.score || 0"
              aria-valuemin="0"
              aria-valuemax="100"
              [attr.aria-label]="
                'AIEO.RESULTS.CATEGORY_ARIA_LABEL' | translate: { category: section?.title, score: section?.score || 0 }
              "
            >
              <svg class="category-gauge-svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <circle
                  class="category-gauge-background"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="var(--border-color, #e0e0e0)"
                  stroke-width="4"
                />
                <circle
                  class="category-gauge-progress"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="url(#category-gauge-gradient)"
                  stroke-width="4"
                  stroke-linecap="round"
                  [attr.stroke-dasharray]="getCategoryGaugeCircumference()"
                  [attr.stroke-dashoffset]="getCategoryGaugeOffset(section?.score || 0)"
                  transform="rotate(-90 50 50)"
                />
                <defs>
                  <linearGradient id="category-gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" [attr.stop-color]="getCategoryGaugeColor(section?.score || 0)" />
                    <stop offset="100%" [attr.stop-color]="getCategoryGaugeColor(section?.score || 0)" />
                  </linearGradient>
                </defs>
              </svg>
              <div class="category-gauge-center">
                <div class="category-gauge-score">{{ section?.score || 0 }}%</div>
                <div class="category-gauge-label">{{ getCategoryScoreLabel(section?.score || 0) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Content -->
        <div class="category-content-column">
          <!-- Recommendations Section -->
          <div class="recommendations-section" *ngIf="hasRecommendations(section)">
            <h4 class="recommendations-title">Recommendations</h4>
            <div class="recommendations-list">
              <div
                *ngFor="let recommendation of section.recommendations; trackBy: trackByRecommendation"
                class="recommendation-item"
              >
                {{ recommendation }}
              </div>
            </div>
          </div>

          <!-- Summary Section -->
          <div class="summary-section" *ngIf="hasSummary(section)">
            <h4 class="summary-title">Summary</h4>
            <div class="summary-content">
              {{ section.summary }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="audit-footer">
    <button mat-raised-button color="primary" class="run-another-button" (click)="onRunAnotherAudit()">
      {{ 'AIEO.RESULTS.RUN_ANOTHER_AUDIT' | translate }}
    </button>
  </div>
</div>
