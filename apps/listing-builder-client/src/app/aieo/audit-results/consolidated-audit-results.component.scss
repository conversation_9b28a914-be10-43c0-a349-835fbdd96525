.consolidated-audit-results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.audit-section {
  margin-bottom: 32px;
}

.audit-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.audit-card-content {
  padding: 24px;
}

.progress-card-layout {
  display: flex;
  align-items: center;
  gap: 32px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 24px;
  }
}

.progress-column {
  flex-shrink: 0;
}

.content-column {
  flex: 1;
}

.circular-gauge-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gauge-wrapper {
  position: relative;
  width: 140px;
  height: 140px;
}

.gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.gauge-background {
  stroke: var(--border-color, #e0e0e0);
}

.gauge-progress {
  transition: stroke-dashoffset 0.8s ease-in-out;
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.gauge-score {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary, #333);
  line-height: 1;
}

.gauge-label {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.audit-summary {
  h2.overall-score-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary, #333);
    margin: 0 0 16px 0;
  }

  .audit-description {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-secondary, #666);
    margin: 0 0 20px 0;
  }
}

.report-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.audit-section-header {
  margin-bottom: 16px;

  .section-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin: 0;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-color, #1976d2);
  }
}

.audit-section-content {
  background: var(--background-paper, #fff);
  border-radius: 8px;
  padding: 24px;
  border: 1px solid var(--border-color, #e0e0e0);
}

.category-layout {
  display: flex;
  gap: 32px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 24px;
  }
}

.category-gauge-column {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category-gauge-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.category-gauge-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
}

.category-gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.category-gauge-background {
  stroke: var(--border-color, #e0e0e0);
}

.category-gauge-progress {
  transition: stroke-dashoffset 0.8s ease-in-out;
}

.category-gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.category-gauge-score {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary, #333);
  line-height: 1;
}

.category-gauge-label {
  font-size: 10px;
  color: var(--text-secondary, #666);
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page-scores-section {
  .page-scores-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin: 0 0 12px 0;
  }

  .page-scores-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .page-score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--background-default, #f5f5f5);
    border-radius: 6px;
    font-size: 13px;

    .page-name {
      font-weight: 500;
      color: var(--text-primary, #333);
    }

    .page-score {
      font-weight: 700;
      color: var(--primary-color, #1976d2);
    }
  }
}

.category-content-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.recommendations-section,
.summary-section {
  h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
  }
}

.recommendations-list,
.summary-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item,
.summary-point {
  padding: 12px 16px;
  background: var(--background-default, #f8f9fa);
  border-left: 4px solid var(--primary-color, #1976d2);
  border-radius: 0 6px 6px 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #333);

  &:before {
    content: "•";
    color: var(--primary-color, #1976d2);
    font-weight: bold;
    margin-right: 8px;
  }
}

.audit-footer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid var(--border-color, #e0e0e0);

  .run-another-button {
    padding: 12px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .consolidated-audit-results-container {
    padding: 16px;
  }

  .audit-card-content {
    padding: 20px;
  }

  .audit-section-content {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .consolidated-audit-results-container {
    padding: 12px;
  }

  .audit-card-content {
    padding: 16px;
  }

  .audit-section-content {
    padding: 16px;
  }

  .gauge-wrapper {
    width: 120px;
    height: 120px;
  }

  .gauge-score {
    font-size: 20px;
  }

  .gauge-label {
    font-size: 11px;
  }

  .category-gauge-wrapper {
    width: 80px;
    height: 80px;
  }

  .category-gauge-score {
    font-size: 16px;
  }

  .category-gauge-label {
    font-size: 9px;
  }

  .audit-summary h2.overall-score-title {
    font-size: 24px;
  }

  .audit-section-header .section-title {
    font-size: 20px;
  }
}

// Animation for gauge progress
.gauge-progress,
.category-gauge-progress {
  animation: gaugeFill 1.5s ease-out forwards;
}

@keyframes gaugeFill {
  from {
    stroke-dashoffset: 345.575; // Full circumference
  }
  to {
    stroke-dashoffset: var(--gauge-offset);
  }
}

// Accessibility improvements
.gauge-wrapper,
.category-gauge-wrapper {
  &:focus {
    outline: 2px solid var(--primary-color, #1976d2);
    outline-offset: 2px;
  }
}

// Print styles
@media print {
  .consolidated-audit-results-container {
    max-width: none;
    padding: 0;
  }

  .audit-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .audit-footer {
    display: none;
  }

  .report-actions {
    display: none;
  }
} 