import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from '../../shared/shared.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyProgressBarModule } from '@vendasta/galaxy/progress-bar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { TranslateService } from '@ngx-translate/core';
import { TranslateModule } from '@ngx-translate/core';
import { 
  AuditConsolidationService, 
  ConsolidatedAuditData, 
  ConsolidatedCategory,
  MultiPageAuditData 
} from './audit-consolidation.service';

@Component({
  selector: 'app-consolidated-audit-results',
  templateUrl: './consolidated-audit-results.component.html',
  styleUrls: ['./consolidated-audit-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatButtonModule,
    MatIconModule,
    SharedModule,
    GalaxyFormFieldModule,
    GalaxyProgressBarModule,
    GalaxyBadgeModule,
    TranslateModule,
  ],
  standalone: true,
})
export class ConsolidatedAuditResultsComponent implements OnChanges {
  @Input() multiPageAuditData: MultiPageAuditData | null = null;
  @Output() runAnotherAudit = new EventEmitter<void>();

  consolidatedData: ConsolidatedAuditData | null = null;
  textReport: string = '';

  constructor(
    private translateService: TranslateService,
    private auditConsolidationService: AuditConsolidationService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['multiPageAuditData'] && this.multiPageAuditData) {
      this.consolidatedData = this.auditConsolidationService.consolidateAuditData(this.multiPageAuditData);
      this.textReport = this.auditConsolidationService.generateTextReport(this.consolidatedData);
    }
  }

  onRunAnotherAudit(): void {
    this.runAnotherAudit.emit();
  }

  trackByCategory(index: number, category: ConsolidatedCategory): string {
    return category.name;
  }

  trackByRecommendation(index: number, recommendation: string): number {
    return index;
  }

  trackBySummaryPoint(index: number, summaryPoint: string): number {
    return index;
  }

  get overallScore(): number {
    return this.consolidatedData?.overallScore || 0;
  }

  get overallDescription(): string {
    return this.consolidatedData?.overallDescription || '';
  }

  get categories(): ConsolidatedCategory[] {
    return this.consolidatedData?.categories || [];
  }

  get scoreLabel(): string {
    return this.getScoreLabel(this.overallScore);
  }

  getScoreLabel(score: number): string {
    if (score >= 80) return this.translateService.instant('AIEO.SCORES.EXCELLENT');
    if (score >= 60) return this.translateService.instant('AIEO.SCORES.VERY_GOOD');
    if (score >= 40) return this.translateService.instant('AIEO.SCORES.GOOD');
    if (score >= 20) return this.translateService.instant('AIEO.SCORES.FAIR');
    return this.translateService.instant('AIEO.SCORES.POOR');
  }

  getGaugeCircumference(): number {
    return 2 * Math.PI * 55;
  }

  getGaugeOffset(): number {
    const circumference = this.getGaugeCircumference();
    const progress = this.overallScore / 100;
    return circumference * (1 - progress);
  }

  getGaugeColor(percentage: number): string {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 60) return '#8BC34A';
    if (percentage >= 40) return '#FFC107';
    if (percentage >= 20) return '#FF9800';
    return '#F44336';
  }

  getCategoryGaugeCircumference(): number {
    return 2 * Math.PI * 40;
  }

  getCategoryGaugeOffset(score: number): number {
    const circumference = this.getCategoryGaugeCircumference();
    const progress = score / 100;
    return circumference * (1 - progress);
  }

  getCategoryGaugeColor(score: number): string {
    return this.getGaugeColor(score);
  }

  getCategoryScoreLabel(score: number): string {
    return this.getScoreLabel(score);
  }

  hasRecommendations(category: ConsolidatedCategory): boolean {
    return category?.recommendations && category.recommendations.length > 0;
  }

  hasSummary(category: ConsolidatedCategory): boolean {
    return category?.summary && category.summary.length > 0;
  }

  hasMultiplePages(category: ConsolidatedCategory): boolean {
    return category?.pageScores && category.pageScores.length > 1;
  }

  downloadTextReport(): void {
    const blob = new Blob([this.textReport], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'consolidated-audit-report.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
} 