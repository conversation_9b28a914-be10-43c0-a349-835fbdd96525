export interface ConsolidatedAuditData {
  overallScore: number;
  overallDescription: string;
  categories: ConsolidatedCategory[];
}

export interface ConsolidatedCategory {
  name: string;
  averageScore: number;
  pageScores: PageScore[];
  recommendations: string[];
  summary: string[];
}

export interface PageScore {
  url: string;
  score: number;
}

export interface MultiPageAuditData {
  overallScore: number;
  overallDescription: string;
  pages: PageAuditData[];
}

export interface PageAuditData {
  url: string;
  sections: AuditSection[];
}

export interface AuditSection {
  title: string;
  recommendations: string[];
  summary: string;
  score: number;
}

export class AuditConsolidationService {
  private readonly MAIN_CATEGORIES = [
    'Schema Markup',
    'Structured Data', 
    'Robots.txt Enablement',
    'Metadata',
    'Content Quality',
    'Performance'
  ];

  consolidateAuditData(multiPageData: MultiPageAuditData): ConsolidatedAuditData {
    const consolidatedCategories: ConsolidatedCategory[] = [];
    
    for (const categoryName of this.MAIN_CATEGORIES) {
      const categoryData = this.processCategory(categoryName, multiPageData.pages);
      if (categoryData) {
        consolidatedCategories.push(categoryData);
      }
    }

    return {
      overallScore: multiPageData.overallScore,
      overallDescription: multiPageData.overallDescription,
      categories: consolidatedCategories
    };
  }


  private processCategory(categoryName: string, pages: PageAuditData[]): ConsolidatedCategory | null {
    const categorySections: AuditSection[] = [];
    const pageScores: PageScore[] = [];

    for (const page of pages) {
      const categorySection = page.sections.find(section => 
        this.normalizeCategoryName(section.title) === this.normalizeCategoryName(categoryName)
      );
      
      if (categorySection) {
        categorySections.push(categorySection);
        pageScores.push({
          url: this.extractPageName(page.url),
          score: categorySection.score
        });
      }
    }

    if (categorySections.length === 0) {
      return null;
    }

    const totalScore = categorySections.reduce((sum, section) => sum + section.score, 0);
    const averageScore = Math.round((totalScore / categorySections.length) * 10) / 10;

    const allRecommendations = categorySections.flatMap(section => section.recommendations);
    const uniqueRecommendations = this.deduplicateRecommendations(allRecommendations);

    const allSummaries = categorySections.map(section => section.summary);
    const consolidatedSummary = this.consolidateSummaries(allSummaries);

    return {
      name: categoryName,
      averageScore,
      pageScores,
      recommendations: uniqueRecommendations,
      summary: consolidatedSummary
    };
  }


  private normalizeCategoryName(name: string): string {
    return name.toLowerCase().trim();
  }


  private extractPageName(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      if (pathname === '/' || pathname === '') {
        return 'Homepage';
      }
      const parts = pathname.split('/').filter(part => part.length > 0);
      return parts.length > 0 ? parts[parts.length - 1] : 'Homepage';
    } catch {
      return url;
    }
  }


  private deduplicateRecommendations(recommendations: string[]): string[] {
    const seen = new Set<string>();
    const unique: string[] = [];
    
    for (const recommendation of recommendations) {
      const normalized = this.normalizeRecommendation(recommendation);
      if (!seen.has(normalized)) {
        seen.add(normalized);
        unique.push(recommendation);
      }
    }
    
    return unique;
  }


  private normalizeRecommendation(recommendation: string): string {
    return recommendation.toLowerCase().trim().replace(/\s+/g, ' ');
  }


  private consolidateSummaries(summaries: string[]): string[] {
    const allSummaryPoints: string[] = [];
    
    for (const summary of summaries) {
      if (summary.trim()) {
        const points = this.splitSummaryIntoPoints(summary);
        allSummaryPoints.push(...points);
      }
    }

    return this.deduplicateSummaryPoints(allSummaryPoints);
  }


  private splitSummaryIntoPoints(summary: string): string[] {
    const delimiters = ['. ', '; ', '\n', '• ', '- '];
    let points = [summary];
    
    for (const delimiter of delimiters) {
      points = points.flatMap(point => 
        point.split(delimiter).map(p => p.trim()).filter(p => p.length > 0)
      );
    }
    
    return points.filter(point => point.length > 10); 
  }

  private deduplicateSummaryPoints(points: string[]): string[] {
    const normalizedPoints = new Map<string, string>();
    
    for (const point of points) {
      const normalized = this.normalizeSummaryPoint(point);
      if (!normalizedPoints.has(normalized) && point.length > 10) {
        normalizedPoints.set(normalized, point);
      }
    }
    
    return Array.from(normalizedPoints.values());
  }


  private normalizeSummaryPoint(point: string): string {
    return point.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  
  generateTextReport(consolidatedData: ConsolidatedAuditData): string {
    let report = '';

    report += `Overall Website Score\n`;
    report += `-----------------\n`;
    report += `Average Score: ${consolidatedData.overallScore}%\n\n`;
    report += `${consolidatedData.overallDescription}\n\n`;

    for (const category of consolidatedData.categories) {
      report += this.formatCategorySection(category);
    }

    return report;
  }


  private formatCategorySection(category: ConsolidatedCategory): string {
    let section = `${category.name}\n`;
    section += `-----------------\n`;
    
    if (category.pageScores.length > 1) {
      section += `Average Score: ${category.averageScore}%\n`;
      section += `Page Scores: ${category.pageScores.map(ps => `${ps.url}: ${ps.score}%`).join(', ')}\n\n`;
    } else {
      section += `Score: ${category.averageScore}%\n\n`;
    }

    if (category.recommendations.length > 0) {
      section += `Overall Recommendations:\n`;
      for (const recommendation of category.recommendations) {
        section += `- ${recommendation}\n`;
      }
      section += `\n`;
    }

    if (category.summary.length > 0) {
      section += `Overall Summary:\n`;
      for (const summaryPoint of category.summary) {
        section += `- ${summaryPoint}\n`;
      }
      section += `\n`;
    }

    section += `---\n\n`;
    return section;
  }
} 