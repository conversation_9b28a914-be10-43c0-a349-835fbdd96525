<div class="consolidated-audit-results-container" *ngIf="consolidatedData">
  <div class="audit-section">
    <mat-card appearance="outlined" class="audit-card overall-score-card">
      <mat-card-content class="audit-card-content">
        <div class="progress-card-layout">
          <div class="progress-column">
            <div class="circular-gauge-container">
              <div
                class="gauge-wrapper"
                role="progressbar"
                [attr.aria-valuenow]="overallScore"
                aria-valuemin="0"
                aria-valuemax="100"
                [attr.aria-label]="'AIEO.RESULTS.ARIA_LABEL' | translate: { score: overallScore }"
              >
                <svg class="gauge-svg" viewBox="0 0 140 140" xmlns="http://www.w3.org/2000/svg">
                  <circle
                    class="gauge-background"
                    cx="70"
                    cy="70"
                    r="55"
                    fill="none"
                    stroke="var(--border-color, #e0e0e0)"
                    stroke-width="6"
                  />
                  <circle
                    class="gauge-progress"
                    cx="70"
                    cy="70"
                    r="55"
                    fill="none"
                    stroke="url(#gauge-gradient)"
                    stroke-width="6"
                    stroke-linecap="round"
                    [attr.stroke-dasharray]="getGaugeCircumference()"
                    [attr.stroke-dashoffset]="getGaugeOffset()"
                    transform="rotate(-90 70 70)"
                  />
                  <defs>
                    <linearGradient id="gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" [attr.stop-color]="getGaugeColor(0)" />
                      <stop offset="50%" [attr.stop-color]="getGaugeColor(50)" />
                      <stop offset="100%" [attr.stop-color]="getGaugeColor(100)" />
                    </linearGradient>
                  </defs>
                </svg>
                <div class="gauge-center">
                  <div class="gauge-score">{{ overallScore }}%</div>
                  <div class="gauge-label">{{ scoreLabel }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-column">
            <div class="audit-summary">
              <h2 class="overall-score-title">Overall Website Score</h2>
              <p class="audit-description">
                {{ overallDescription }}
              </p>
              <div class="report-actions">
                <button mat-button color="primary" (click)="downloadTextReport()">
                  <mat-icon>download</mat-icon>
                  Download Report
                </button>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Category Sections -->
  <div class="audit-section" *ngFor="let category of categories; trackBy: trackByCategory">
    <div class="audit-section-header">
      <h3 class="section-title">{{ category.name }}</h3>
    </div>
    <div class="audit-section-content">
      <div class="category-layout">
        <!-- Left Side - Score Display -->
        <div class="category-gauge-column">
          <div class="category-gauge-container">
            <div
              class="category-gauge-wrapper"
              role="progressbar"
              [attr.aria-valuenow]="category.averageScore"
              aria-valuemin="0"
              aria-valuemax="100"
              [attr.aria-label]="'AIEO.RESULTS.CATEGORY_ARIA_LABEL' | translate: { category: category.name, score: category.averageScore }"
            >
              <svg class="category-gauge-svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <circle
                  class="category-gauge-background"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="var(--border-color, #e0e0e0)"
                  stroke-width="4"
                />
                <circle
                  class="category-gauge-progress"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="url(#category-gauge-gradient)"
                  stroke-width="4"
                  stroke-linecap="round"
                  [attr.stroke-dasharray]="getCategoryGaugeCircumference()"
                  [attr.stroke-dashoffset]="getCategoryGaugeOffset(category.averageScore)"
                  transform="rotate(-90 50 50)"
                />
                <defs>
                  <linearGradient id="category-gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" [attr.stop-color]="getCategoryGaugeColor(category.averageScore)" />
                    <stop offset="100%" [attr.stop-color]="getCategoryGaugeColor(category.averageScore)" />
                  </linearGradient>
                </defs>
              </svg>
              <div class="category-gauge-center">
                <div class="category-gauge-score">{{ category.averageScore }}%</div>
                <div class="category-gauge-label">{{ getCategoryScoreLabel(category.averageScore) }}</div>
              </div>
            </div>
          </div>
          
          <!-- Page Scores Display -->
          <div class="page-scores-section" *ngIf="hasMultiplePages(category)">
            <h4 class="page-scores-title">Page Scores</h4>
            <div class="page-scores-list">
              <div 
                *ngFor="let pageScore of category.pageScores; trackBy: trackByRecommendation" 
                class="page-score-item"
              >
                <span class="page-name">{{ pageScore.url }}:</span>
                <span class="page-score">{{ pageScore.score }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Content -->
        <div class="category-content-column">
          <!-- Summary Section -->
          <div class="summary-section" *ngIf="hasSummary(category)">
            <h4 class="summary-title">Overall Summary</h4>
            <div class="summary-content">
              <div 
                *ngFor="let summaryPoint of category.summary; trackBy: trackBySummaryPoint" 
                class="summary-point"
              >
                {{ summaryPoint }}
              </div>
            </div>
          </div>
          
          <!-- Recommendations Section -->
          <div class="recommendations-section" *ngIf="hasRecommendations(category)">
            <h4 class="recommendations-title">Overall Recommendations</h4>
            <div class="recommendations-list">
              <div 
                *ngFor="let recommendation of category.recommendations; trackBy: trackByRecommendation" 
                class="recommendation-item"
              >
                {{ recommendation }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="audit-footer">
    <button mat-raised-button color="primary" class="run-another-button" (click)="onRunAnotherAudit()">
      {{ 'AIEO.RESULTS.RUN_ANOTHER_AUDIT' | translate }}
    </button>
  </div>
</div> 